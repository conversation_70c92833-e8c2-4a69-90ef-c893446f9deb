"""
Excel Handler Module for NSE Archive File Download Automation

This module handles reading data from the BRD_Automation_RAW.xlsx file:
- Reads main data from Sheet1
- Reads individual Sr. No. sheets for URL and target file information
- Updates status and remarks back to Excel
"""

import pandas as pd
import openpyxl
from pathlib import Path
import logging
from typing import Dict, List, Tuple, Optional


class ExcelHandler:
    """Handles all Excel operations for the NSE automation"""
    
    def __init__(self, excel_path: str):
        """
        Initialize Excel handler
        
        Args:
            excel_path: Path to the Excel file
        """
        self.excel_path = Path(excel_path)
        self.workbook = None
        self.logger = logging.getLogger(__name__)
        
        if not self.excel_path.exists():
            raise FileNotFoundError(f"Excel file not found: {excel_path}")
    
    def load_workbook(self):
        """Load the Excel workbook"""
        try:
            self.workbook = openpyxl.load_workbook(self.excel_path)
            self.logger.info(f"Loaded Excel workbook: {self.excel_path}")
        except Exception as e:
            self.logger.error(f"Failed to load workbook: {e}")
            raise
    
    def get_sheet1_data(self) -> List[Dict]:
        """
        Read data from Sheet1
        
        Returns:
            List of dictionaries containing Sr. No., File Name, File Location, etc.
        """
        try:
            # Read Sheet1 starting from row 5 (header is in row 4)
            df = pd.read_excel(self.excel_path, sheet_name="Sheet1", header=3)
            
            # Filter out empty rows
            df = df.dropna(subset=['Sr. No.', 'File Name', 'File Location'])
            
            # Convert to list of dictionaries
            data = []
            for _, row in df.iterrows():
                if pd.notna(row['Sr. No.']) and pd.notna(row['File Name']) and pd.notna(row['File Location']):
                    data.append({
                        'sr_no': int(row['Sr. No.']),
                        'file_name': str(row['File Name']).strip(),
                        'file_location': str(row['File Location']).strip(),
                        'status': str(row.get('Status', '')).strip(),
                        'remark': str(row.get('Remark', '')).strip(),
                        'priority': row.get('Priority', 1),
                        'exchange': str(row.get('Exchange', 'NSE')).strip()
                    })
            
            self.logger.info(f"Read {len(data)} records from Sheet1")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to read Sheet1: {e}")
            raise
    
    def get_sr_sheet_data(self, sr_no: int) -> Tuple[str, str]:
        """
        Read data from individual Sr. No. sheet

        Args:
            sr_no: Serial number to read

        Returns:
            Tuple of (url, target_file_name)
        """
        try:
            # Try different sheet name formats
            possible_names = [f"Sr. No. {sr_no}", f"Sr. No.{sr_no}"]

            sheet_name = None
            for name in possible_names:
                if name in self.workbook.sheetnames:
                    sheet_name = name
                    break

            if not sheet_name:
                raise ValueError(f"Sheet for Sr. No. {sr_no} not found. Available sheets: {self.workbook.sheetnames}")

            # Read the sheet without headers
            df = pd.read_excel(self.excel_path, sheet_name=sheet_name, header=None)

            # Extract URL from A1 (row 0, col 0)
            url = str(df.iloc[0, 0]).strip() if pd.notna(df.iloc[0, 0]) else ""

            # Extract target file name from A4 (row 3, col 0)
            target_file_name = str(df.iloc[3, 0]).strip() if pd.notna(df.iloc[3, 0]) else ""

            # Validate URL format (should start with http)
            if not url.startswith('http'):
                raise ValueError(f"Invalid URL format in cell A1 of sheet {sheet_name}: {url}")

            if not target_file_name:
                raise ValueError(f"No target file name found in cell A4 of sheet {sheet_name}")

            self.logger.info(f"Read data from {sheet_name}: URL={url}, Target={target_file_name}")
            return url, target_file_name

        except Exception as e:
            self.logger.error(f"Failed to read Sr. No. {sr_no} sheet: {e}")
            raise
    
    def update_status(self, sr_no: int, status: str, remark: str = ""):
        """
        Update status and remark for a specific Sr. No. in Sheet1

        Args:
            sr_no: Serial number to update
            status: Status to set
            remark: Remark to set
        """
        try:
            if not self.workbook:
                self.load_workbook()

            sheet = self.workbook["Sheet1"]

            # Find the row for this Sr. No. (starting from row 5, as header is in row 4)
            for row in range(5, sheet.max_row + 1):
                if sheet[f"A{row}"].value == sr_no:
                    # Update Status (Column F) and Remark (Column G)
                    sheet[f"F{row}"] = status
                    sheet[f"G{row}"] = remark
                    break

            # Save the workbook (create a new workbook instance to avoid file handle issues)
            temp_workbook = openpyxl.load_workbook(self.excel_path)
            temp_sheet = temp_workbook["Sheet1"]

            # Find and update the row again in the temp workbook
            for row in range(5, temp_sheet.max_row + 1):
                if temp_sheet[f"A{row}"].value == sr_no:
                    temp_sheet[f"F{row}"] = status
                    temp_sheet[f"G{row}"] = remark
                    break

            temp_workbook.save(self.excel_path)
            temp_workbook.close()

            self.logger.info(f"Updated Sr. No. {sr_no}: Status={status}, Remark={remark}")

        except Exception as e:
            self.logger.error(f"Failed to update status for Sr. No. {sr_no}: {e}")
            raise
    
    def close(self):
        """Close the workbook"""
        if self.workbook:
            self.workbook.close()
            self.workbook = None
