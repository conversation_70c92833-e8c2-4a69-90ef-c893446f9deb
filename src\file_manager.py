"""
File Management Module for NSE Archive File Download Automation

This module handles file operations:
- Monitors downloads
- Renames files
- Moves files to target locations
- Creates directories
- Handles file conflicts
"""

import shutil
import time
import logging
from pathlib import Path
from typing import Optional, List
import os


class FileManager:
    """Handles file operations for the NSE automation"""
    
    def __init__(self, config: dict):
        """
        Initialize file manager
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.download_dir = Path(config['download']['temp_directory'])
        self.download_dir.mkdir(exist_ok=True)
    
    def wait_for_download_completion(self, expected_filename: str = None, timeout: int = 120) -> Optional[Path]:
        """
        Wait for download to complete
        
        Args:
            expected_filename: Expected filename (optional)
            timeout: Timeout in seconds
            
        Returns:
            Path to downloaded file if found, None otherwise
        """
        try:
            self.logger.info(f"Waiting for download completion (timeout: {timeout}s)")
            
            start_time = time.time()
            initial_files = set(self.download_dir.glob("*"))
            
            while time.time() - start_time < timeout:
                current_files = set(self.download_dir.glob("*"))
                new_files = current_files - initial_files
                
                # Check for new files that are not being downloaded (.crdownload, .tmp, etc.)
                for file_path in new_files:
                    if self._is_download_complete(file_path):
                        if expected_filename:
                            if expected_filename.lower() in file_path.name.lower():
                                self.logger.info(f"Download completed: {file_path}")
                                return file_path
                        else:
                            self.logger.info(f"Download completed: {file_path}")
                            return file_path
                
                time.sleep(1)  # Check every second
            
            self.logger.warning(f"Download timeout after {timeout} seconds")
            return None
            
        except Exception as e:
            self.logger.error(f"Error waiting for download: {e}")
            return None
    
    def _is_download_complete(self, file_path: Path) -> bool:
        """
        Check if download is complete
        
        Args:
            file_path: Path to check
            
        Returns:
            True if download is complete, False otherwise
        """
        try:
            # Skip temporary download files
            if file_path.suffix in ['.crdownload', '.tmp', '.part']:
                return False
            
            # Check if file exists and is not empty
            if not file_path.exists() or file_path.stat().st_size == 0:
                return False
            
            # Check if file is still being written to
            initial_size = file_path.stat().st_size
            time.sleep(0.5)
            
            if not file_path.exists():
                return False
                
            current_size = file_path.stat().st_size
            
            # If size hasn't changed, download is likely complete
            return initial_size == current_size
            
        except Exception:
            return False
    
    def rename_and_move_file(self, source_path: Path, target_filename: str, target_directory: str) -> bool:
        """
        Rename and move file to target location
        
        Args:
            source_path: Source file path
            target_filename: Target filename
            target_directory: Target directory path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            target_dir = Path(target_directory)
            target_path = target_dir / target_filename
            
            self.logger.info(f"Moving file: {source_path} -> {target_path}")
            
            # Create target directory if it doesn't exist
            if self.config['files']['create_directories']:
                target_dir.mkdir(parents=True, exist_ok=True)
                self.logger.info(f"Created directory: {target_dir}")
            
            # Handle existing file
            if target_path.exists():
                if not self.config['files']['overwrite_existing']:
                    if self.config['files']['backup_existing']:
                        backup_path = self._create_backup_path(target_path)
                        shutil.move(str(target_path), str(backup_path))
                        self.logger.info(f"Backed up existing file to: {backup_path}")
                    else:
                        self.logger.error(f"Target file already exists: {target_path}")
                        return False
            
            # Move and rename the file
            shutil.move(str(source_path), str(target_path))
            self.logger.info(f"File moved successfully: {target_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to move file {source_path} to {target_directory}: {e}")
            return False
    
    def _create_backup_path(self, original_path: Path) -> Path:
        """
        Create a backup path for existing file
        
        Args:
            original_path: Original file path
            
        Returns:
            Backup file path
        """
        timestamp = int(time.time())
        backup_name = f"{original_path.stem}_backup_{timestamp}{original_path.suffix}"
        return original_path.parent / backup_name
    
    def cleanup_temp_files(self):
        """Clean up temporary download files"""
        try:
            temp_files = list(self.download_dir.glob("*"))
            
            for file_path in temp_files:
                try:
                    if file_path.is_file():
                        file_path.unlink()
                        self.logger.info(f"Cleaned up temp file: {file_path}")
                except Exception as e:
                    self.logger.warning(f"Failed to clean up {file_path}: {e}")
            
            self.logger.info("Temporary files cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def validate_file_extension(self, filename: str) -> bool:
        """
        Validate file extension
        
        Args:
            filename: Filename to validate
            
        Returns:
            True if extension is allowed, False otherwise
        """
        file_path = Path(filename)
        extension = file_path.suffix.lower()
        
        allowed_extensions = self.config['files']['allowed_extensions']
        
        if extension in allowed_extensions:
            return True
        
        self.logger.warning(f"File extension not allowed: {extension}")
        return False
    
    def get_file_info(self, file_path: Path) -> dict:
        """
        Get file information
        
        Args:
            file_path: Path to file
            
        Returns:
            Dictionary with file information
        """
        try:
            stat = file_path.stat()
            
            return {
                'name': file_path.name,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'extension': file_path.suffix.lower(),
                'exists': file_path.exists()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get file info for {file_path}: {e}")
            return {}
    
    def find_downloaded_files(self, pattern: str = "*") -> List[Path]:
        """
        Find downloaded files matching pattern
        
        Args:
            pattern: File pattern to match
            
        Returns:
            List of matching file paths
        """
        try:
            return list(self.download_dir.glob(pattern))
        except Exception as e:
            self.logger.error(f"Error finding files with pattern {pattern}: {e}")
            return []
