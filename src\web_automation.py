"""
Web Automation Module for NSE Archive File Download

This module handles web automation using Playwright:
- Navigates to NSE websites
- Searches for target files
- Handles downloads
- Manages browser sessions
"""

from playwright.sync_api import sync_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext, Download
import time
import logging
from pathlib import Path
from typing import Optional, List
import re


class NSEWebAutomation:
    """Handles web automation for NSE file downloads"""
    
    def __init__(self, config: dict):
        """
        Initialize web automation
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.logger = logging.getLogger(__name__)
        self.download_dir = Path(config['download']['temp_directory'])
        self.download_dir.mkdir(exist_ok=True)
    
    def start_browser(self):
        """Start the browser session"""
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.launch(
                headless=self.config['browser']['headless']
            )
            
            # Create context with download settings
            self.context = self.browser.new_context(
                accept_downloads=True,
                viewport={'width': 1920, 'height': 1080}
            )
            
            # Set timeouts
            self.context.set_default_timeout(self.config['browser']['timeout'])
            
            self.page = self.context.new_page()
            self.logger.info("Browser started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start browser: {e}")
            raise
    
    def navigate_to_url(self, url: str) -> bool:
        """
        Navigate to the specified URL
        
        Args:
            url: URL to navigate to
            
        Returns:
            True if navigation successful, False otherwise
        """
        try:
            self.logger.info(f"Navigating to: {url}")
            self.page.goto(url, wait_until='networkidle')
            
            # Wait for page to load
            time.sleep(self.config['nse']['base_wait_time'] / 1000)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to navigate to {url}: {e}")
            return False
    
    def search_for_file(self, target_file_name: str) -> Optional[str]:
        """
        Search for the target file on the current page
        
        Args:
            target_file_name: Name of the file to search for
            
        Returns:
            Download URL if found, None otherwise
        """
        try:
            self.logger.info(f"Searching for file: {target_file_name}")
            
            # Wait for dynamic content to load
            time.sleep(self.config['nse']['dynamic_content_wait'] / 1000)
            
            # Search strategies
            download_url = self._search_in_links(target_file_name)
            if download_url:
                return download_url
            
            download_url = self._search_in_tables(target_file_name)
            if download_url:
                return download_url
            
            download_url = self._search_in_text_elements(target_file_name)
            if download_url:
                return download_url
            
            self.logger.warning(f"File not found on current page: {target_file_name}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error searching for file {target_file_name}: {e}")
            return None
    
    def _search_in_links(self, target_file_name: str) -> Optional[str]:
        """Search for file in anchor tags"""
        try:
            links = self.page.locator("a").all()
            
            for link in links:
                try:
                    text = link.inner_text().strip()
                    href = link.get_attribute("href")
                    
                    if self._is_file_match(text, target_file_name) and href:
                        self.logger.info(f"Found file link: {text} -> {href}")
                        return href
                        
                except Exception:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error searching in links: {e}")
            return None
    
    def _search_in_tables(self, target_file_name: str) -> Optional[str]:
        """Search for file in table cells"""
        try:
            # Look for table cells containing the file name
            cells = self.page.locator("td, th").all()
            
            for cell in cells:
                try:
                    text = cell.inner_text().strip()
                    
                    if self._is_file_match(text, target_file_name):
                        # Look for download link in the same row
                        row = cell.locator("xpath=ancestor::tr[1]")
                        download_links = row.locator("a[href*='download'], a[href*='.csv'], a[href*='.zip'], a[href*='.dat'], a[href*='.txt']").all()
                        
                        for link in download_links:
                            href = link.get_attribute("href")
                            if href:
                                self.logger.info(f"Found file in table: {text} -> {href}")
                                return href
                                
                except Exception:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error searching in tables: {e}")
            return None
    
    def _search_in_text_elements(self, target_file_name: str) -> Optional[str]:
        """Search for file in various text elements"""
        try:
            # Search in divs, spans, and other text elements
            elements = self.page.locator("div, span, p").all()
            
            for element in elements:
                try:
                    text = element.inner_text().strip()
                    
                    if self._is_file_match(text, target_file_name):
                        # Look for nearby download links
                        parent = element.locator("xpath=parent::*")
                        download_links = parent.locator("a[href*='download'], a[href*='.csv'], a[href*='.zip'], a[href*='.dat'], a[href*='.txt']").all()
                        
                        for link in download_links:
                            href = link.get_attribute("href")
                            if href:
                                self.logger.info(f"Found file in text element: {text} -> {href}")
                                return href
                                
                except Exception:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error searching in text elements: {e}")
            return None
    
    def _is_file_match(self, text: str, target_file_name: str) -> bool:
        """
        Check if text matches the target file name
        
        Args:
            text: Text to check
            target_file_name: Target file name
            
        Returns:
            True if match found, False otherwise
        """
        if not text or not target_file_name:
            return False
        
        # Case-insensitive exact match
        if target_file_name.lower() in text.lower():
            return True
        
        # Remove common variations and check again
        clean_text = re.sub(r'[^\w\s.-]', '', text.lower())
        clean_target = re.sub(r'[^\w\s.-]', '', target_file_name.lower())
        
        if clean_target in clean_text:
            return True
        
        return False

    def download_file(self, download_url: str) -> Optional[Path]:
        """
        Download file from the given URL

        Args:
            download_url: URL to download from

        Returns:
            Path to downloaded file if successful, None otherwise
        """
        try:
            self.logger.info(f"Attempting to download: {download_url}")

            # Handle relative URLs
            if download_url.startswith('/'):
                base_url = f"{self.page.url.split('/')[0]}//{self.page.url.split('/')[2]}"
                download_url = base_url + download_url

            # Set up download listener
            with self.page.expect_download(timeout=self.config['browser']['download_timeout']) as download_info:
                # Navigate to download URL or click the link
                if download_url.startswith('http'):
                    self.page.goto(download_url)
                else:
                    # Try to find and click the link
                    link = self.page.locator(f"a[href='{download_url}']").first
                    if link.is_visible():
                        link.click()
                    else:
                        self.logger.error(f"Download link not found: {download_url}")
                        return None

            download = download_info.value

            # Save the download
            download_path = self.download_dir / download.suggested_filename
            download.save_as(download_path)

            self.logger.info(f"File downloaded successfully: {download_path}")
            return download_path

        except Exception as e:
            self.logger.error(f"Failed to download file from {download_url}: {e}")
            return None

    def scroll_and_search(self, target_file_name: str) -> Optional[str]:
        """
        Scroll through the page and search for the target file

        Args:
            target_file_name: Name of the file to search for

        Returns:
            Download URL if found, None otherwise
        """
        try:
            max_scrolls = self.config['download']['max_scroll_attempts']
            scroll_delay = self.config['download']['scroll_delay']

            for scroll_count in range(max_scrolls):
                self.logger.info(f"Scroll attempt {scroll_count + 1}/{max_scrolls}")

                # Search on current view
                download_url = self.search_for_file(target_file_name)
                if download_url:
                    return download_url

                # Scroll down
                self.page.mouse.wheel(0, 1000)
                time.sleep(scroll_delay / 1000)

                # Check if we've reached the bottom
                if self._is_at_bottom():
                    self.logger.info("Reached bottom of page")
                    break

            self.logger.warning(f"File not found after {max_scrolls} scroll attempts")
            return None

        except Exception as e:
            self.logger.error(f"Error during scroll and search: {e}")
            return None

    def _is_at_bottom(self) -> bool:
        """Check if page is scrolled to bottom"""
        try:
            return self.page.evaluate("window.innerHeight + window.scrollY >= document.body.offsetHeight")
        except:
            return False

    def handle_pagination(self, target_file_name: str) -> Optional[str]:
        """
        Handle pagination and search across multiple pages

        Args:
            target_file_name: Name of the file to search for

        Returns:
            Download URL if found, None otherwise
        """
        try:
            max_pages = self.config['download']['max_pagination_attempts']

            for page_count in range(max_pages):
                self.logger.info(f"Searching on page {page_count + 1}/{max_pages}")

                # Search on current page with scrolling
                download_url = self.scroll_and_search(target_file_name)
                if download_url:
                    return download_url

                # Look for next page button
                next_button = self._find_next_button()
                if not next_button:
                    self.logger.info("No next page button found")
                    break

                # Click next page
                next_button.click()
                time.sleep(self.config['nse']['base_wait_time'] / 1000)

            self.logger.warning(f"File not found after searching {max_pages} pages")
            return None

        except Exception as e:
            self.logger.error(f"Error during pagination: {e}")
            return None

    def _find_next_button(self):
        """Find the next page button"""
        try:
            # Common next button selectors
            selectors = [
                "a:has-text('Next')",
                "button:has-text('Next')",
                "a:has-text('>')",
                "button:has-text('>')",
                ".pagination a:has-text('Next')",
                ".pager a:has-text('Next')"
            ]

            for selector in selectors:
                button = self.page.locator(selector).first
                if button.is_visible():
                    return button

            return None

        except Exception:
            return None

    def close(self):
        """Close browser session"""
        try:
            if self.page:
                self.page.close()
            if self.context:
                self.context.close()
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()

            self.logger.info("Browser session closed")

        except Exception as e:
            self.logger.error(f"Error closing browser: {e}")
