"""
Test Excel functionality only
"""

import sys
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from excel_handler import ExcelHandler


def test_excel_read_only():
    """Test Excel reading without any modifications"""
    try:
        print("Testing Excel read-only operations...")
        
        eh = ExcelHandler('BRD_Automation_RAW.xlsx')
        eh.load_workbook()
        
        # Test reading Sheet1
        data = eh.get_sheet1_data()
        print(f"✓ Read {len(data)} records from Sheet1")
        
        # Test reading valid Sr. No. sheets
        valid_sr_nos = []
        for record in data:
            sr_no = record['sr_no']
            try:
                url, target_file = eh.get_sr_sheet_data(sr_no)
                if url.startswith('http'):
                    valid_sr_nos.append(sr_no)
                    print(f"✓ Sr. No. {sr_no}: {url[:50]}... -> {target_file[:30]}...")
            except Exception as e:
                print(f"- Sr. No. {sr_no}: {e}")
        
        print(f"\n✓ Found {len(valid_sr_nos)} valid Sr. No. sheets with NSE URLs")
        print(f"Valid Sr. Nos: {valid_sr_nos}")
        
        eh.close()
        return True
        
    except Exception as e:
        print(f"✗ Excel test failed: {e}")
        return False


if __name__ == "__main__":
    success = test_excel_read_only()
    if success:
        print("\n✓ Excel functionality is working correctly!")
    else:
        print("\n✗ Excel functionality has issues.")
