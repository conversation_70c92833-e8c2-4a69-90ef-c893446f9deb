"""
NSE Archive File Download Automation - Entry Point

This is the main entry point for running the NSE automation.
It provides command-line interface and handles initialization.
"""

import sys
import argparse
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from nse_downloader import NSEDownloader


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="NSE Archive File Download Automation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_automation.py                    # Run with default config
  python run_automation.py --config custom.yaml  # Run with custom config
  python run_automation.py --test-single 6       # Test with single Sr. No.
        """
    )
    
    parser.add_argument(
        "--config", 
        default="config/config.yaml",
        help="Path to configuration file (default: config/config.yaml)"
    )
    
    parser.add_argument(
        "--test-single",
        type=int,
        help="Test with a single Sr. No. (for debugging)"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Perform dry run without actual downloads"
    )
    
    args = parser.parse_args()
    
    try:
        print("=" * 60)
        print("NSE Archive File Download Automation")
        print("=" * 60)
        
        # Check if config file exists
        if not Path(args.config).exists():
            print(f"Error: Configuration file not found: {args.config}")
            sys.exit(1)
        
        # Check if Excel file exists
        import yaml
        with open(args.config, 'r') as f:
            config = yaml.safe_load(f)
        
        excel_path = Path(config['excel']['file_path'])
        if not excel_path.exists():
            print(f"Error: Excel file not found: {excel_path}")
            print("Please ensure BRD_Automation_RAW.xlsx is in the current directory")
            sys.exit(1)
        
        # Initialize and run downloader
        downloader = NSEDownloader(args.config)

        if args.test_single:
            print(f"Running test with Sr. No. {args.test_single}")
            downloader.run(test_sr_no=args.test_single)
        elif args.dry_run:
            print("Dry run mode - no actual downloads will be performed")
            # TODO: Implement dry run mode
            downloader.run()
        else:
            downloader.run()
        
        print("\nAutomation completed successfully!")
        
    except KeyboardInterrupt:
        print("\nAutomation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nAutomation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
