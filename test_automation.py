"""
Test script for NSE Archive File Download Automation

This script tests individual components before running the full automation.
"""

import sys
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from excel_handler import ExcelHandler
from web_automation import NSEWebAutomation
from file_manager import FileManager
import yaml


def test_excel_handler():
    """Test Excel handler functionality"""
    print("=" * 50)
    print("Testing Excel Handler")
    print("=" * 50)
    
    try:
        eh = ExcelHandler('BRD_Automation_RAW.xlsx')
        eh.load_workbook()
        
        # Test reading Sheet1
        data = eh.get_sheet1_data()
        print(f"✓ Successfully read {len(data)} records from Sheet1")
        
        if data:
            print(f"✓ First record: Sr. No. {data[0]['sr_no']}, File: {data[0]['file_name']}")
        
        # Test reading Sr. No. sheet
        if data:
            sr_no = data[0]['sr_no']
            url, target_file = eh.get_sr_sheet_data(sr_no)
            print(f"✓ Sr. No. {sr_no} - URL: {url}")
            print(f"✓ Target file: {target_file}")
        
        eh.close()
        print("✓ Excel handler test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Excel handler test failed: {e}")
        return False


def test_config_loading():
    """Test configuration loading"""
    print("\n" + "=" * 50)
    print("Testing Configuration Loading")
    print("=" * 50)
    
    try:
        with open('config/config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        print("✓ Configuration loaded successfully")
        print(f"✓ Browser headless: {config['browser']['headless']}")
        print(f"✓ Download directory: {config['download']['temp_directory']}")
        print(f"✓ Max scroll attempts: {config['download']['max_scroll_attempts']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_file_manager():
    """Test file manager functionality"""
    print("\n" + "=" * 50)
    print("Testing File Manager")
    print("=" * 50)
    
    try:
        with open('config/config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        fm = FileManager(config)
        
        # Test directory creation
        download_dir = Path(config['download']['temp_directory'])
        if download_dir.exists():
            print(f"✓ Download directory exists: {download_dir}")
        else:
            print(f"✗ Download directory not found: {download_dir}")
            return False
        
        # Test file validation
        test_files = ['test.csv', 'test.txt', 'test.zip', 'test.dat']
        for file in test_files:
            is_valid = fm.validate_file_extension(file)
            print(f"✓ File {file} validation: {is_valid}")
        
        print("✓ File manager test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ File manager test failed: {e}")
        return False


def test_web_automation_init():
    """Test web automation initialization (without browser launch)"""
    print("\n" + "=" * 50)
    print("Testing Web Automation Initialization")
    print("=" * 50)
    
    try:
        with open('config/config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Just test initialization without starting browser
        wa = NSEWebAutomation(config)
        print("✓ Web automation initialized successfully")
        
        # Test download directory
        if wa.download_dir.exists():
            print(f"✓ Download directory ready: {wa.download_dir}")
        
        return True
        
    except Exception as e:
        print(f"✗ Web automation initialization failed: {e}")
        return False


def main():
    """Run all tests"""
    print("NSE Archive File Download Automation - Component Tests")
    print("=" * 60)
    
    tests = [
        test_config_loading,
        test_excel_handler,
        test_file_manager,
        test_web_automation_init
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The automation is ready to run.")
        print("\nTo run the full automation:")
        print("  python run_automation.py")
        print("\nTo test with a single Sr. No.:")
        print("  python run_automation.py --test-single 6")
    else:
        print("✗ Some tests failed. Please check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
