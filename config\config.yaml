# NSE Archive File Download Automation Configuration

# Excel file settings
excel:
  file_path: "BRD_Automation_RAW.xlsx"
  main_sheet: "Sheet1"
  sr_no_column: "A"
  file_name_column: "B"
  file_location_column: "C"
  status_column: "F"
  remark_column: "G"

# Browser settings
browser:
  headless: false
  timeout: 30000  # 30 seconds
  download_timeout: 120000  # 2 minutes
  page_load_timeout: 30000  # 30 seconds

# Download settings
download:
  temp_directory: "downloads"
  max_scroll_attempts: 20
  scroll_delay: 1000  # 1 second
  max_pagination_attempts: 10
  search_timeout: 5000  # 5 seconds

# Logging settings
logging:
  level: "INFO"
  file: "logs/download_log.txt"
  format: "%(asctime)s - %(levelname)s - %(message)s"
  max_file_size: 10485760  # 10MB
  backup_count: 5

# NSE website settings
nse:
  base_wait_time: 3000  # 3 seconds
  dynamic_content_wait: 2000  # 2 seconds
  download_wait_time: 5000  # 5 seconds
  retry_attempts: 3
  retry_delay: 2000  # 2 seconds

# File management
files:
  create_directories: true
  overwrite_existing: false
  backup_existing: true
  allowed_extensions: [".csv", ".txt", ".dat", ".zip", ".xlsx", ".xls"]
