"""
NSE Archive File Download Automation - Main Orchestrator

This is the main script that coordinates all components:
- Excel reading
- Web automation
- File management
- Logging and error handling
"""

import yaml
import logging
import logging.handlers
from pathlib import Path
import sys
import time
from typing import Dict, List

from excel_handler import ExcelHandler
from web_automation import NSEWebAutomation
from file_manager import FileManager


class NSEDownloader:
    """Main orchestrator for NSE file downloads"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        Initialize NSE Downloader
        
        Args:
            config_path: Path to configuration file
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logging()
        
        # Initialize components
        self.excel_handler = None
        self.web_automation = None
        self.file_manager = None
        
        self.logger.info("NSE Downloader initialized")
    
    def _load_config(self, config_path: str) -> dict:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as file:
                config = yaml.safe_load(file)
            return config
        except Exception as e:
            print(f"Failed to load config: {e}")
            sys.exit(1)
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        try:
            # Create logs directory
            log_dir = Path(self.config['logging']['file']).parent
            log_dir.mkdir(exist_ok=True)
            
            # Configure logging
            logger = logging.getLogger()
            logger.setLevel(getattr(logging, self.config['logging']['level']))
            
            # File handler with rotation
            file_handler = logging.handlers.RotatingFileHandler(
                self.config['logging']['file'],
                maxBytes=self.config['logging']['max_file_size'],
                backupCount=self.config['logging']['backup_count']
            )
            
            # Console handler
            console_handler = logging.StreamHandler()
            
            # Formatter
            formatter = logging.Formatter(self.config['logging']['format'])
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # Add handlers
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
            
            return logger
            
        except Exception as e:
            print(f"Failed to setup logging: {e}")
            sys.exit(1)
    
    def initialize_components(self):
        """Initialize all components"""
        try:
            self.logger.info("Initializing components...")
            
            # Initialize Excel handler
            self.excel_handler = ExcelHandler(self.config['excel']['file_path'])
            self.excel_handler.load_workbook()
            
            # Initialize web automation
            self.web_automation = NSEWebAutomation(self.config)
            self.web_automation.start_browser()
            
            # Initialize file manager
            self.file_manager = FileManager(self.config)
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            raise
    
    def process_downloads(self):
        """Process all downloads from Excel data"""
        try:
            self.logger.info("Starting download process...")
            
            # Get data from Sheet1
            sheet1_data = self.excel_handler.get_sheet1_data()
            
            if not sheet1_data:
                self.logger.warning("No data found in Sheet1")
                return
            
            self.logger.info(f"Found {len(sheet1_data)} records to process")
            
            # Process each record
            success_count = 0
            failure_count = 0
            
            for record in sheet1_data:
                try:
                    success = self._process_single_record(record)
                    if success:
                        success_count += 1
                    else:
                        failure_count += 1
                        
                except Exception as e:
                    self.logger.error(f"Error processing Sr. No. {record['sr_no']}: {e}")
                    failure_count += 1
                    self.excel_handler.update_status(
                        record['sr_no'], 
                        "Failed", 
                        f"Error: {str(e)[:100]}"
                    )
            
            self.logger.info(f"Download process completed. Success: {success_count}, Failed: {failure_count}")
            
        except Exception as e:
            self.logger.error(f"Error in download process: {e}")
            raise
    
    def _process_single_record(self, record: Dict) -> bool:
        """
        Process a single download record
        
        Args:
            record: Record dictionary from Sheet1
            
        Returns:
            True if successful, False otherwise
        """
        sr_no = record['sr_no']
        target_filename = record['file_name']
        target_location = record['file_location']
        
        self.logger.info(f"Processing Sr. No. {sr_no}: {target_filename}")
        
        try:
            # Update status to In Progress
            self.excel_handler.update_status(sr_no, "In Progress", "Starting download...")
            
            # Get URL and search filename from Sr. No. sheet
            url, search_filename = self.excel_handler.get_sr_sheet_data(sr_no)
            
            self.logger.info(f"Sr. No. {sr_no} - URL: {url}, Search for: {search_filename}")
            
            # Navigate to URL
            if not self.web_automation.navigate_to_url(url):
                raise Exception("Failed to navigate to URL")
            
            # Search for file with pagination
            download_url = self.web_automation.handle_pagination(search_filename)
            
            if not download_url:
                raise Exception(f"File not found: {search_filename}")
            
            # Download the file
            downloaded_file = self.web_automation.download_file(download_url)
            
            if not downloaded_file:
                raise Exception("Download failed")
            
            # Wait for download completion
            completed_file = self.file_manager.wait_for_download_completion(
                search_filename, 
                timeout=self.config['browser']['download_timeout'] // 1000
            )
            
            if not completed_file:
                completed_file = downloaded_file
            
            # Validate file
            if not self.file_manager.validate_file_extension(completed_file.name):
                self.logger.warning(f"File extension validation failed for: {completed_file.name}")
            
            # Move and rename file
            success = self.file_manager.rename_and_move_file(
                completed_file, 
                target_filename, 
                target_location
            )
            
            if success:
                self.excel_handler.update_status(sr_no, "Successful", "Download completed")
                self.logger.info(f"Successfully processed Sr. No. {sr_no}")
                return True
            else:
                raise Exception("Failed to move file to target location")
                
        except Exception as e:
            error_msg = str(e)
            self.excel_handler.update_status(sr_no, "Failed", error_msg[:100])
            self.logger.error(f"Failed to process Sr. No. {sr_no}: {error_msg}")
            return False
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            self.logger.info("Cleaning up resources...")
            
            if self.file_manager:
                self.file_manager.cleanup_temp_files()
            
            if self.web_automation:
                self.web_automation.close()
            
            if self.excel_handler:
                self.excel_handler.close()
            
            self.logger.info("Cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def run(self):
        """Main execution method"""
        try:
            self.logger.info("Starting NSE Archive File Download Automation")
            
            # Initialize components
            self.initialize_components()
            
            # Process downloads
            self.process_downloads()
            
            self.logger.info("NSE Archive File Download Automation completed successfully")
            
        except Exception as e:
            self.logger.error(f"NSE Downloader failed: {e}")
            raise
        finally:
            self.cleanup()


if __name__ == "__main__":
    downloader = NSEDownloader()
    downloader.run()
