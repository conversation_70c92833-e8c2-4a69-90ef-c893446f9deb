# NSE Archive File Download Automation

This Python automation tool downloads specific files from NSE (National Stock Exchange) archive pages based on data from an Excel file. It uses <PERSON><PERSON> for web automation and handles file downloads, renaming, and organization automatically.

## Features

- **Excel Integration**: Reads download requirements from `BRD_Automation_RAW.xlsx`
- **Web Automation**: Uses <PERSON><PERSON> to navigate NSE websites and find files
- **Smart Search**: Searches through pages with scrolling and pagination support
- **File Management**: Downloads, renames, and moves files to specified locations
- **Comprehensive Logging**: Detailed logs for monitoring and troubleshooting
- **Status Updates**: Updates Excel file with download status and remarks
- **Error Handling**: Robust error handling with retry mechanisms

## Project Structure

```
nse_downloader/
├── config/
│   └── config.yaml              # Configuration settings
├── src/
│   ├── excel_handler.py         # Excel operations
│   ├── web_automation.py        # Playwright web automation
│   ├── file_manager.py          # File operations
│   └── nse_downloader.py        # Main orchestrator
├── logs/
│   └── download_log.txt         # Application logs
├── downloads/                   # Temporary download directory
├── BRD_Automation_RAW.xlsx      # Input Excel file
├── requirements.txt             # Python dependencies
├── run_automation.py            # Entry point script
└── README.md                    # This file
```

## Setup Instructions

### 1. Prerequisites

- Python 3.9 or higher
- Windows environment (tested on Windows)

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Install Playwright Browsers

```bash
playwright install chromium
```

### 4. Prepare Excel File

Ensure `BRD_Automation_RAW.xlsx` is in the project root directory with:

- **Sheet1**: Contains Sr. No., File Name, File Location columns
- **Sr. No. sheets**: Individual sheets named "Sr. No. X" with:
  - A1: NSE archive URL
  - A4: Target file name to search for

## Usage

### Basic Usage

```bash
python run_automation.py
```

### Advanced Usage

```bash
# Use custom configuration
python run_automation.py --config custom_config.yaml

# Test with single Sr. No. (for debugging)
python run_automation.py --test-single 6

# Dry run mode (no actual downloads)
python run_automation.py --dry-run
```

## Configuration

Edit `config/config.yaml` to customize:

- **Browser settings**: Headless mode, timeouts
- **Download settings**: Scroll attempts, pagination limits
- **File settings**: Allowed extensions, backup options
- **Logging settings**: Log level, file rotation

## Excel File Format

### Sheet1 Structure
| Sr. No. | File Name | File Location | Status | Remark |
|---------|-----------|---------------|--------|--------|
| 1 | contract.txt | C:\Files\contract.txt | | |
| 2 | security.txt | C:\Files\security.txt | | |

### Sr. No. Sheet Structure
- **A1**: NSE URL (e.g., https://www.nseindia.com/all-reports-derivatives)
- **A2**: Section name (e.g., DERIVATIVES)
- **A3**: Sub-section (e.g., Daily Reports)
- **A4**: Target file name to search (e.g., F&O-UDiFF Common Bhavcopy Final (zip))

## How It Works

1. **Read Excel Data**: Loads Sheet1 to get list of files to download
2. **Process Each Record**: For each Sr. No.:
   - Reads corresponding sheet for URL and target file name
   - Navigates to NSE website
   - Searches for the target file (with scrolling/pagination)
   - Downloads the file
   - Renames and moves to specified location
   - Updates status in Excel
3. **Logging**: All activities are logged for monitoring and debugging

## Troubleshooting

### Common Issues

1. **Browser not found**: Run `playwright install chromium`
2. **Excel file not found**: Ensure `BRD_Automation_RAW.xlsx` is in project root
3. **Download timeout**: Increase timeout in config.yaml
4. **File not found**: Check if target file name in A4 matches exactly

### Logs

Check `logs/download_log.txt` for detailed information about:
- Navigation steps
- Search attempts
- Download progress
- Error messages

### Debug Mode

Use `--test-single X` to test with a specific Sr. No. for debugging.

## Support

For issues or questions:
1. Check the log files for error details
2. Verify Excel file format matches requirements
3. Test with a single record first
4. Ensure NSE website accessibility

## License

This project is for internal use and automation purposes.
